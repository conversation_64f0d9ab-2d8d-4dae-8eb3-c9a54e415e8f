"""
统一钻井检测系统V2.0

融合前驱信号检测、异常检测、专家经验三种钻井卡钻检测算法的综合检测平台。

主要模块：
- config: 配置管理模块
- core: 核心检测模块（数据处理、算法适配、融合引擎、批量处理）
- utils: 工具模块（数据转换、文件夹管理、日志）

使用方式：
1. 单文件检测：python -m unified_detection_system_v2.main --input data.csv
2. 批量检测：python -m unified_detection_system_v2.main --batch-input folder/
3. 系统测试：python -m unified_detection_system_v2.test_import

版本：V2.0
作者：统一钻井检测系统开发团队
"""

__version__ = "2.0.0"
__author__ = "统一钻井检测系统开发团队"

# 导入主要组件，便于外部使用
from .core.fusion_engine import UnifiedFusionEngine
from .core.batch_processor import BatchProcessingManager
from .config.config import config_manager

__all__ = [
    'UnifiedFusionEngine',
    'BatchProcessingManager', 
    'config_manager',
    '__version__',
    '__author__'
]
