"""
统一钻井检测系统V2.0 - 算法适配器
"""

import os
import sys
import subprocess
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from config.config import config_manager
from utils.logger import get_logger


class BaseAlgorithmAdapter:
    """算法适配器基类"""
    
    def __init__(self, algorithm_name: str):
        self.algorithm_name = algorithm_name
        self.config = config_manager.config
        self.logger = get_logger(f"{algorithm_name}Adapter")
    
    def predict(self, input_data: str) -> Dict[str, Any]:
        """预测接口（子类必须实现）"""
        raise NotImplementedError("子类必须实现predict方法")
    
    def validate_input(self, input_data: str) -> bool:
        """验证输入数据"""
        return os.path.exists(input_data)
    
    def normalize_output(self, raw_output: Any) -> Dict[str, Any]:
        """标准化输出格式"""
        return {
            'algorithm': self.algorithm_name,
            'success': True,
            'risk_score': 0.0,
            'prediction': 0,
            'confidence': 0.0,
            'details': {}
        }


class PrecursorDetectionAdapter(BaseAlgorithmAdapter):
    """前驱信号检测适配器"""
    
    def __init__(self):
        super().__init__("precursor")
        self.script_path = "../前驱信号检测/run.py"
    
    def predict(self, input_data: str) -> Dict[str, Any]:
        """前驱信号检测预测"""
        try:
            self.logger.info("开始前驱信号检测")
            
            if not self.validate_input(input_data):
                raise ValueError(f"输入数据无效: {input_data}")
            
            # 调用前驱信号检测算法
            result = self.call_precursor_algorithm(input_data)
            
            # 标准化输出
            normalized_result = self.normalize_precursor_output(result)
            
            self.logger.info("前驱信号检测完成")
            return normalized_result
            
        except Exception as e:
            self.logger.error(f"前驱信号检测失败: {e}")
            return self.get_error_result(str(e))
    
    def call_precursor_algorithm(self, data_path: str) -> Dict[str, Any]:
        """调用前驱信号检测算法"""
        try:
            # 构建命令行参数
            cmd = [
                sys.executable, self.script_path,
                "--task_name", "earlysignaldet",
                "--is_training", "0",
                "--model_id", "PatchTST",
                "--model", "PatchTST",
                "--data", "custom",
                "--root_path", data_path,
                "--data_path", ".",
                "--features", "M",
                "--seq_len", "152",
                "--enc_in", "10",
                "--d_model", "128",
                "--n_heads", "16",
                "--e_layers", "3",
                "--d_layers", "1",
                "--d_ff", "256",
                "--dropout", "0.2",
                "--fc_dropout", "0.2",
                "--head_dropout", "0.0",
                "--patch_len", "16",
                "--stride", "8",
                "--des", "Exp",
                "--train_epochs", "100",
                "--patience", "20",
                "--itr", "1"
            ]
            
            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd="../前驱信号检测",
                timeout=300  # 5分钟超时
            )
            
            if result.returncode != 0:
                raise RuntimeError(f"前驱信号检测执行失败: {result.stderr}")
            
            # 解析结果
            return self.parse_precursor_output(result.stdout)
            
        except Exception as e:
            self.logger.error(f"调用前驱信号检测算法失败: {e}")
            raise
    
    def parse_precursor_output(self, output: str) -> Dict[str, Any]:
        """解析前驱信号检测输出"""
        try:
            # 简化的输出解析
            # 实际实现需要根据算法的具体输出格式进行解析
            
            # 查找预测结果
            lines = output.split('\n')
            predictions = []
            
            for line in lines:
                if 'prediction' in line.lower() or 'result' in line.lower():
                    # 提取预测值
                    # 这里需要根据实际输出格式进行调整
                    pass
            
            # 默认返回结果
            return {
                'predictions': [0.3],  # 示例风险分数
                'labels': [0],         # 示例预测标签
                'confidence': 0.8      # 示例置信度
            }
            
        except Exception as e:
            self.logger.error(f"解析前驱信号检测输出失败: {e}")
            return {'predictions': [0.0], 'labels': [0], 'confidence': 0.0}
    
    def normalize_precursor_output(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """标准化前驱信号检测输出"""
        try:
            predictions = result.get('predictions', [0.0])
            labels = result.get('labels', [0])
            confidence = result.get('confidence', 0.0)
            
            # 计算平均风险分数
            risk_score = np.mean(predictions) if predictions else 0.0
            prediction = int(np.mean(labels)) if labels else 0
            
            return {
                'algorithm': self.algorithm_name,
                'success': True,
                'risk_score': float(risk_score),
                'prediction': prediction,
                'confidence': float(confidence),
                'details': {
                    'raw_predictions': predictions,
                    'raw_labels': labels
                }
            }
            
        except Exception as e:
            self.logger.error(f"标准化前驱信号检测输出失败: {e}")
            return self.get_error_result(str(e))
    
    def get_error_result(self, error_msg: str) -> Dict[str, Any]:
        """获取错误结果"""
        return {
            'algorithm': self.algorithm_name,
            'success': False,
            'risk_score': 0.0,
            'prediction': 0,
            'confidence': 0.0,
            'error': error_msg,
            'details': {}
        }


class SimplifiedAnomalyDetectionAdapter(BaseAlgorithmAdapter):
    """简化异常检测适配器（仅重建误差阈值）"""
    
    def __init__(self):
        super().__init__("anomaly")
        self.script_path = "../异常检测/run.py"
        self.anomaly_threshold = self.config.get('algorithms.anomaly.anomaly_ratio', 1.0)
    
    def predict(self, input_data: str) -> Dict[str, Any]:
        """简化异常检测预测"""
        try:
            self.logger.info("开始简化异常检测")
            
            if not self.validate_input(input_data):
                raise ValueError(f"输入数据无效: {input_data}")
            
            # 调用简化异常检测算法
            result = self.call_simplified_anomaly_algorithm(input_data)
            
            # 标准化输出
            normalized_result = self.normalize_anomaly_output(result)
            
            self.logger.info("简化异常检测完成")
            return normalized_result
            
        except Exception as e:
            self.logger.error(f"简化异常检测失败: {e}")
            return self.get_error_result(str(e))
    
    def call_simplified_anomaly_algorithm(self, npy_file: str) -> Dict[str, Any]:
        """调用简化异常检测算法"""
        try:
            # 构建命令行参数（仅重建误差阈值方法）
            cmd = [
                sys.executable, self.script_path,
                "--task_name", "anomaly_detection",
                "--is_training", "0",
                "--model_id", "FEDformer",
                "--model", "FEDformer",
                "--data", "custom",
                "--root_path", os.path.dirname(npy_file),
                "--data_path", os.path.basename(npy_file),
                "--features", "M",
                "--seq_len", "96",
                "--enc_in", "12",
                "--d_model", "128",
                "--anomaly_ratio", str(self.anomaly_threshold)
            ]
            
            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd="../异常检测",
                timeout=300  # 5分钟超时
            )
            
            if result.returncode != 0:
                raise RuntimeError(f"异常检测执行失败: {result.stderr}")
            
            # 解析结果
            return self.parse_anomaly_output(result.stdout)
            
        except Exception as e:
            self.logger.error(f"调用简化异常检测算法失败: {e}")
            raise
    
    def parse_anomaly_output(self, output: str) -> Dict[str, Any]:
        """解析异常检测输出"""
        try:
            # 简化的输出解析
            lines = output.split('\n')
            anomaly_ratio = 0.0
            
            for line in lines:
                if '异常比例' in line or 'anomaly ratio' in line.lower():
                    # 提取异常比例
                    # 这里需要根据实际输出格式进行调整
                    pass
            
            # 默认返回结果
            return {
                'anomaly_ratio': 0.5,  # 示例异常比例
                'threshold': self.anomaly_threshold
            }
            
        except Exception as e:
            self.logger.error(f"解析异常检测输出失败: {e}")
            return {'anomaly_ratio': 0.0, 'threshold': self.anomaly_threshold}
    
    def normalize_anomaly_output(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """标准化异常检测输出"""
        try:
            anomaly_ratio = result.get('anomaly_ratio', 0.0)
            threshold = result.get('threshold', self.anomaly_threshold)
            
            # 基于异常比例计算风险分数和预测标签
            risk_score = min(anomaly_ratio / 100.0, 1.0)  # 转换为0-1范围
            prediction = 1 if anomaly_ratio > threshold else 0
            confidence = abs(anomaly_ratio - threshold) / threshold if threshold > 0 else 0.0
            
            return {
                'algorithm': self.algorithm_name,
                'success': True,
                'risk_score': float(risk_score),
                'prediction': prediction,
                'confidence': float(min(confidence, 1.0)),
                'details': {
                    'anomaly_ratio': anomaly_ratio,
                    'threshold': threshold
                }
            }
            
        except Exception as e:
            self.logger.error(f"标准化异常检测输出失败: {e}")
            return self.get_error_result(str(e))
    
    def get_error_result(self, error_msg: str) -> Dict[str, Any]:
        """获取错误结果"""
        return {
            'algorithm': self.algorithm_name,
            'success': False,
            'risk_score': 0.0,
            'prediction': 0,
            'confidence': 0.0,
            'error': error_msg,
            'details': {}
        }


class PureRulesExpertAdapter(BaseAlgorithmAdapter):
    """纯规则专家系统适配器（不依赖机器学习模型）"""

    def __init__(self):
        super().__init__("expert")
        self.window_size = self.config.get('algorithms.expert.window_size', 30)
        self.score_threshold = self.config.get('algorithms.expert.score_threshold', 4)
        self.rules_config = self.config.get('algorithms.expert.rules_config', {})

    def predict(self, input_data: str) -> Dict[str, Any]:
        """纯规则专家系统预测"""
        try:
            self.logger.info("开始纯规则专家系统检测")

            if not self.validate_input(input_data):
                raise ValueError(f"输入数据无效: {input_data}")

            # 读取CSV数据
            df = pd.read_csv(input_data, encoding='utf-8')

            # 应用6个核心规则
            rule_results = self.apply_expert_rules(df)

            # 标准化输出
            normalized_result = self.normalize_expert_output(rule_results)

            self.logger.info("纯规则专家系统检测完成")
            return normalized_result

        except Exception as e:
            self.logger.error(f"纯规则专家系统检测失败: {e}")
            return self.get_error_result(str(e))

    def apply_expert_rules(self, df: pd.DataFrame) -> Dict[str, Any]:
        """应用6个核心专家规则"""
        try:
            rule_scores = []
            rule_details = {}

            # 检查数据长度
            if len(df) < self.window_size:
                self.logger.warning(f"数据长度 {len(df)} 小于窗口大小 {self.window_size}，使用全部数据")
                # 如果数据不足，使用全部数据作为一个窗口
                window_data = df
                window_score = 0

                # 应用所有规则
                window_score += self.rule_depth_change(window_data)
                window_score += self.rule_bit_depth_change(window_data)
                window_score += self.rule_rpm_anomaly(window_data)
                window_score += self.rule_hook_height_change(window_data)
                window_score += self.rule_hookload_change(window_data)
                window_score += self.rule_flow_difference(window_data)

                rule_scores.append(window_score)
            else:
                # 使用滑动窗口处理数据
                for i in range(len(df) - self.window_size + 1):
                    window_data = df.iloc[i:i + self.window_size]
                    window_score = 0

                    # 规则1: 井深变化检测
                    depth_score = self.rule_depth_change(window_data)
                    window_score += depth_score

                    # 规则2: 钻头深度变化检测
                    bit_score = self.rule_bit_depth_change(window_data)
                    window_score += bit_score

                    # 规则3: 转速异常检测
                    rpm_score = self.rule_rpm_anomaly(window_data)
                    window_score += rpm_score

                    # 规则4: 大钩高度变化检测
                    hook_score = self.rule_hook_height_change(window_data)
                    window_score += hook_score

                    # 规则5: 大钩载荷变化检测
                    hookload_score = self.rule_hookload_change(window_data)
                    window_score += hookload_score

                    # 规则6: 流量差异检测
                    flow_score = self.rule_flow_difference(window_data)
                    window_score += flow_score

                    rule_scores.append(window_score)

            # 计算总体结果
            max_score = max(rule_scores) if rule_scores else 0
            avg_score = np.mean(rule_scores) if rule_scores else 0

            rule_details = {
                'max_score': max_score,
                'avg_score': avg_score,
                'score_threshold': self.score_threshold,
                'window_count': len(rule_scores)
            }

            return rule_details

        except Exception as e:
            self.logger.error(f"应用专家规则失败: {e}")
            return {'max_score': 0, 'avg_score': 0, 'score_threshold': self.score_threshold}

    def rule_depth_change(self, window_data: pd.DataFrame) -> float:
        """规则1: 井深变化检测"""
        try:
            if 'depth' not in window_data.columns:
                return 0.0

            depth_change = abs(window_data['depth'].iloc[-1] - window_data['depth'].iloc[0])
            threshold = self.rules_config.get('depth_change_threshold', 0.001)

            return 1.0 if depth_change < threshold else 0.0

        except Exception:
            return 0.0

    def rule_bit_depth_change(self, window_data: pd.DataFrame) -> float:
        """规则2: 钻头深度变化检测"""
        try:
            if 'bit_depth' not in window_data.columns:
                return 0.0

            bit_change = abs(window_data['bit_depth'].iloc[-1] - window_data['bit_depth'].iloc[0])
            threshold = self.rules_config.get('bit_change_threshold', 0.001)

            return 1.0 if bit_change < threshold else 0.0

        except Exception:
            return 0.0

    def rule_rpm_anomaly(self, window_data: pd.DataFrame) -> float:
        """规则3: 转速异常检测"""
        try:
            if 'rpm' not in window_data.columns:
                return 0.0

            rpm_std = window_data['rpm'].std()
            threshold = self.rules_config.get('rpm_threshold', 5)

            return 1.0 if rpm_std < threshold else 0.0

        except Exception:
            return 0.0

    def rule_hook_height_change(self, window_data: pd.DataFrame) -> float:
        """规则4: 大钩高度变化检测"""
        try:
            if 'hook_height' not in window_data.columns:
                return 0.0

            hook_change = abs(window_data['hook_height'].iloc[-1] - window_data['hook_height'].iloc[0])
            threshold = self.rules_config.get('hook_height_change_threshold', 0.01)

            return 1.0 if hook_change < threshold else 0.0

        except Exception:
            return 0.0

    def rule_hookload_change(self, window_data: pd.DataFrame) -> float:
        """规则5: 大钩载荷变化检测"""
        try:
            if 'hookload' not in window_data.columns:
                return 0.0

            hookload_std = window_data['hookload'].std()
            threshold = self.rules_config.get('hookload_change_threshold', 3)

            return 1.0 if hookload_std < threshold else 0.0

        except Exception:
            return 0.0

    def rule_flow_difference(self, window_data: pd.DataFrame) -> float:
        """规则6: 流量差异检测"""
        try:
            if 'flow_in' not in window_data.columns or 'flow_out' not in window_data.columns:
                return 0.0

            flow_diff = abs(window_data['flow_in'].mean() - window_data['flow_out'].mean())
            threshold = self.rules_config.get('flow_diff_threshold', 15)

            return 1.0 if flow_diff > threshold else 0.0

        except Exception:
            return 0.0

    def normalize_expert_output(self, rule_results: Dict[str, Any]) -> Dict[str, Any]:
        """标准化专家规则输出"""
        try:
            max_score = rule_results.get('max_score', 0)
            avg_score = rule_results.get('avg_score', 0)
            threshold = rule_results.get('score_threshold', self.score_threshold)

            # 基于规则分数计算风险分数和预测标签
            risk_score = min(max_score / 6.0, 1.0)  # 最大6分，标准化到0-1
            prediction = 1 if max_score >= threshold else 0
            confidence = min(max_score / 6.0, 1.0)

            return {
                'algorithm': self.algorithm_name,
                'success': True,
                'risk_score': float(risk_score),
                'prediction': prediction,
                'confidence': float(confidence),
                'details': rule_results
            }

        except Exception as e:
            self.logger.error(f"标准化专家规则输出失败: {e}")
            return self.get_error_result(str(e))

    def get_error_result(self, error_msg: str) -> Dict[str, Any]:
        """获取错误结果"""
        return {
            'algorithm': self.algorithm_name,
            'success': False,
            'risk_score': 0.0,
            'prediction': 0,
            'confidence': 0.0,
            'error': error_msg,
            'details': {}
        }
