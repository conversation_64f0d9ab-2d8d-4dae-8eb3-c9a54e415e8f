"""
统一钻井检测系统V2.0 - 正确的测试启动脚本

这个脚本演示了正确的执行方式，解决相对导入问题。

使用方式：
cd d:/PyCharm/kazuan/mix_algo
python unified_detection_system_v2/run_test.py
"""

import sys
import os
import subprocess

def main():
    """主函数：使用正确的模块执行方式"""
    
    print("="*60)
    print("统一钻井检测系统V2.0 - 正确的测试执行")
    print("="*60)
    
    # 获取项目根目录（mix_algo）
    current_file = os.path.abspath(__file__)
    unified_system_dir = os.path.dirname(current_file)
    project_root = os.path.dirname(unified_system_dir)
    
    print(f"当前文件: {current_file}")
    print(f"统一系统目录: {unified_system_dir}")
    print(f"项目根目录: {project_root}")
    print()
    
    # 切换到项目根目录
    os.chdir(project_root)
    print(f"切换工作目录到: {os.getcwd()}")
    print()
    
    # 执行测试模块
    print("执行命令: python -m unified_detection_system_v2.test_import")
    print("-" * 60)
    
    try:
        # 使用subprocess执行模块
        result = subprocess.run([
            sys.executable, "-m", "unified_detection_system_v2.test_import"
        ], capture_output=False, text=True, cwd=project_root)
        
        print("-" * 60)
        if result.returncode == 0:
            print("✅ 测试执行成功！")
        else:
            print(f"❌ 测试执行失败，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
    
    print("="*60)
    print("测试完成")
    print()
    print("如果测试成功，您可以使用以下命令：")
    print("cd d:/PyCharm/kazuan/mix_algo")
    print("python -m unified_detection_system_v2.main --help")
    print("="*60)

if __name__ == "__main__":
    main()
