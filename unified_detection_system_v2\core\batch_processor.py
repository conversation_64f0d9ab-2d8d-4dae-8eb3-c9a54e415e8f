"""
统一钻井检测系统V2.0 - 批量处理器
"""

import os
import json
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from config.config import config_manager
from utils.logger import get_logger
from utils.folder_manager import FolderStructureManager
from core.fusion_engine import UnifiedFusionEngine


class BatchProcessingManager:
    """批量处理管理器"""
    
    def __init__(self, fusion_engine: Optional[UnifiedFusionEngine] = None):
        self.config = config_manager.config
        self.logger = get_logger("BatchProcessor")
        self.folder_manager = FolderStructureManager()
        
        # 使用传入的融合引擎或创建新的
        self.fusion_engine = fusion_engine or UnifiedFusionEngine()
    
    def process_folder(self, input_folder: str, output_folder: str) -> Dict[str, Any]:
        """处理文件夹中的所有CSV文件"""
        try:
            self.logger.info(f"开始批量处理: {input_folder} -> {output_folder}")
            start_time = datetime.now()
            
            # 创建输出文件夹结构
            folder_structure = self.folder_manager.create_batch_structure(
                input_folder, output_folder
            )
            
            # 获取所有CSV文件
            csv_files = self.folder_manager.get_csv_files(input_folder)
            if not csv_files:
                raise ValueError(f"在 {input_folder} 中未找到CSV文件")
            
            # 处理每个文件
            file_results = {}
            successful_count = 0
            failed_count = 0
            
            for i, csv_file in enumerate(csv_files, 1):
                try:
                    self.logger.info(f"处理文件 {i}/{len(csv_files)}: {csv_file}")
                    
                    # 单文件预测
                    result = self.fusion_engine.predict_single_file(csv_file)
                    
                    if 'error' not in result:
                        successful_count += 1
                    else:
                        failed_count += 1
                    
                    file_results[csv_file] = result
                    
                except Exception as e:
                    self.logger.error(f"处理文件失败 {csv_file}: {e}")
                    failed_count += 1
                    file_results[csv_file] = {
                        'filename': csv_file,
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    }
            
            # 生成汇总报告
            end_time = datetime.now()
            summary_report = self.generate_summary_report(
                file_results, start_time, end_time, successful_count, failed_count
            )
            
            # 保存结果
            output_files = self.save_batch_results(
                file_results, summary_report, folder_structure
            )
            
            self.logger.info("批量处理完成")
            return {
                'summary': summary_report,
                'output_files': output_files,
                'file_results': file_results
            }
            
        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def generate_summary_report(self, file_results: Dict[str, Dict[str, Any]], 
                              start_time: datetime, end_time: datetime,
                              successful_count: int, failed_count: int) -> Dict[str, Any]:
        """生成汇总报告"""
        try:
            # 统计信息
            total_files = len(file_results)
            processing_time = (end_time - start_time).total_seconds()
            
            # 风险分数统计
            risk_scores = []
            predictions = []
            
            for result in file_results.values():
                if 'error' not in result:
                    risk_scores.append(result.get('risk_score', 0.0))
                    predictions.append(result.get('prediction', 0))
            
            # 计算统计指标
            if risk_scores:
                avg_risk_score = sum(risk_scores) / len(risk_scores)
                max_risk_score = max(risk_scores)
                min_risk_score = min(risk_scores)
                warning_count = sum(predictions)
                normal_count = len(predictions) - warning_count
            else:
                avg_risk_score = max_risk_score = min_risk_score = 0.0
                warning_count = normal_count = 0
            
            summary_report = {
                'processing_info': {
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'processing_time_seconds': processing_time,
                    'total_files': total_files,
                    'successful_files': successful_count,
                    'failed_files': failed_count,
                    'success_rate': successful_count / total_files if total_files > 0 else 0.0
                },
                'detection_statistics': {
                    'warning_files': warning_count,
                    'normal_files': normal_count,
                    'warning_rate': warning_count / successful_count if successful_count > 0 else 0.0,
                    'avg_risk_score': avg_risk_score,
                    'max_risk_score': max_risk_score,
                    'min_risk_score': min_risk_score
                },
                'algorithm_performance': self.analyze_algorithm_performance(file_results)
            }
            
            return summary_report
            
        except Exception as e:
            self.logger.error(f"生成汇总报告失败: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def analyze_algorithm_performance(self, file_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """分析算法性能"""
        try:
            algorithm_stats = {
                'precursor': {'success': 0, 'total': 0, 'avg_risk': 0.0},
                'anomaly': {'success': 0, 'total': 0, 'avg_risk': 0.0},
                'expert': {'success': 0, 'total': 0, 'avg_risk': 0.0}
            }
            
            for result in file_results.values():
                if 'algorithm_results' in result:
                    algorithm_results = result['algorithm_results']
                    
                    for algorithm, algo_result in algorithm_results.items():
                        if algorithm in algorithm_stats:
                            algorithm_stats[algorithm]['total'] += 1
                            
                            if algo_result.get('success', False):
                                algorithm_stats[algorithm]['success'] += 1
                                risk_score = algo_result.get('risk_score', 0.0)
                                algorithm_stats[algorithm]['avg_risk'] += risk_score
            
            # 计算平均风险分数
            for algorithm, stats in algorithm_stats.items():
                if stats['success'] > 0:
                    stats['avg_risk'] = stats['avg_risk'] / stats['success']
                    stats['success_rate'] = stats['success'] / stats['total'] if stats['total'] > 0 else 0.0
                else:
                    stats['avg_risk'] = 0.0
                    stats['success_rate'] = 0.0
            
            return algorithm_stats
            
        except Exception as e:
            self.logger.error(f"分析算法性能失败: {e}")
            return {}
    
    def save_batch_results(self, file_results: Dict[str, Dict[str, Any]], 
                          summary_report: Dict[str, Any],
                          folder_structure: Dict[str, str]) -> Dict[str, str]:
        """保存批量处理结果"""
        try:
            output_files = {}
            
            # 保存详细结果（JSON格式）
            detailed_results_file = os.path.join(
                folder_structure['results_dir'], "detailed_results.json"
            )
            with open(detailed_results_file, 'w', encoding='utf-8') as f:
                json.dump(file_results, f, ensure_ascii=False, indent=2)
            output_files['detailed_results'] = detailed_results_file
            
            # 保存汇总报告（JSON格式）
            summary_report_file = os.path.join(
                folder_structure['results_dir'], "summary_report.json"
            )
            with open(summary_report_file, 'w', encoding='utf-8') as f:
                json.dump(summary_report, f, ensure_ascii=False, indent=2)
            output_files['summary_report'] = summary_report_file
            
            # 保存CSV格式的简化报告
            csv_summary_file = os.path.join(
                folder_structure['results_dir'], "results_summary.csv"
            )
            self.save_csv_summary(file_results, csv_summary_file)
            output_files['csv_summary'] = csv_summary_file
            
            self.logger.info("批量处理结果保存完成")
            return output_files
            
        except Exception as e:
            self.logger.error(f"保存批量处理结果失败: {e}")
            return {}
    
    def save_csv_summary(self, file_results: Dict[str, Dict[str, Any]], csv_file: str):
        """保存CSV格式的简化报告"""
        try:
            rows = []
            
            for filename, result in file_results.items():
                row = {
                    'Filename': os.path.basename(filename),
                    'Risk_Score': result.get('risk_score', 0.0),
                    'Prediction': result.get('prediction', 0),
                    'Label': result.get('label', '未知'),
                    'Confidence': result.get('confidence', 0.0),
                    'Processing_Time': result.get('processing_time', 0.0),
                    'Status': '成功' if 'error' not in result else '失败',
                    'Error': result.get('error', '')
                }
                rows.append(row)
            
            df = pd.DataFrame(rows)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
        except Exception as e:
            self.logger.error(f"保存CSV汇总失败: {e}")
    
    def get_processing_progress(self, current: int, total: int) -> str:
        """获取处理进度字符串"""
        percentage = (current / total) * 100 if total > 0 else 0
        return f"进度: {current}/{total} ({percentage:.1f}%)"
