"""
统一钻井检测系统V2.0 - 导入测试脚本

执行方式：
1. 作为模块执行（推荐）：
   cd d:/PyCharm/kazuan/mix_algo
   python -m unified_detection_system_v2.test_import

2. 直接执行（需要路径配置）：
   cd d:/PyCharm/kazuan/mix_algo/unified_detection_system_v2
   python test_import.py
"""

import sys
import os

# 添加项目根目录到Python路径（用于直接执行方式）
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

def test_imports():
    """测试所有模块是否可以正常导入"""
    try:
        print("开始测试模块导入...")
        
        # 测试配置模块
        print("1. 测试配置模块...")
        from config.config import config_manager
        print("   ✓ 配置模块导入成功")

        # 测试工具模块
        print("2. 测试工具模块...")
        from utils.data_converter import CSVToNPYConverter, FeatureAligner
        from utils.folder_manager import FolderStructureManager
        from utils.logger import get_logger
        print("   ✓ 工具模块导入成功")

        # 测试核心模块
        print("3. 测试核心模块...")
        from core.data_processor import DataProcessorV2
        from core.algorithm_adapters import (
            PrecursorDetectionAdapter,
            SimplifiedAnomalyDetectionAdapter,
            PureRulesExpertAdapter
        )
        from core.fusion_engine import UnifiedFusionEngine
        from core.batch_processor import BatchProcessingManager
        print("   ✓ 核心模块导入成功")
        
        # 测试配置加载
        print("4. 测试配置加载...")
        config = config_manager.config
        print(f"   ✓ 配置加载成功，系统版本: {config.get('system', {}).get('version', 'Unknown')}")
        
        # 测试组件初始化
        print("5. 测试组件初始化...")
        fusion_engine = UnifiedFusionEngine()
        print("   ✓ 融合引擎初始化成功")
        
        batch_processor = BatchProcessingManager(fusion_engine)
        print("   ✓ 批量处理器初始化成功")
        
        print("\n🎉 所有模块导入和初始化测试通过！")
        print("系统已准备就绪，可以开始使用。")
        return True
        
    except Exception as e:
        print(f"\n❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_basic_functionality():
    """测试基本功能"""
    try:
        print("\n开始测试基本功能...")
        
        # 测试数据处理器
        print("1. 测试数据处理器...")
        from core.data_processor import DataProcessorV2
        processor = DataProcessorV2()
        print("   ✓ 数据处理器创建成功")

        # 测试算法适配器
        print("2. 测试算法适配器...")
        from core.algorithm_adapters import PureRulesExpertAdapter
        expert_adapter = PureRulesExpertAdapter()
        print("   ✓ 专家规则适配器创建成功")

        # 测试融合引擎
        print("3. 测试融合引擎...")
        from core.fusion_engine import UnifiedFusionEngine
        fusion_engine = UnifiedFusionEngine()
        print(f"   ✓ 融合引擎创建成功，权重: {fusion_engine.weights}")
        
        print("\n🎉 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("="*60)
    print("统一钻井检测系统V2.0 - 系统测试")
    print("="*60)
    
    # 运行导入测试
    import_success = test_imports()
    
    if import_success:
        # 运行基本功能测试
        functionality_success = test_basic_functionality()
        
        if functionality_success:
            print("\n" + "="*60)
            print("✅ 系统测试完全通过！")
            print("可以使用以下命令开始使用系统：")
            print("python main.py --help")
            print("="*60)
        else:
            print("\n" + "="*60)
            print("⚠️  导入成功但基本功能测试失败")
            print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ 系统测试失败，请检查依赖和配置")
        print("="*60)
