"""
统一钻井检测系统V2.0 - 数据预处理器
"""

import os
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from pathlib import Path

from config.config import config_manager
from utils.data_converter import CSVToNPYConverter, FeatureAligner
from utils.folder_manager import FolderStructureManager, TempFileManager
from utils.logger import get_logger


class DataProcessorV2:
    """数据预处理器V2.0"""
    
    def __init__(self):
        self.config = config_manager.config
        self.logger = get_logger("DataProcessor")
        
        # 初始化组件
        self.csv_to_npy_converter = CSVToNPYConverter()
        self.feature_aligner = FeatureAligner()
        self.folder_manager = FolderStructureManager()
        self.temp_manager = TempFileManager()
    
    def prepare_data_for_algorithms(self, csv_file: str) -> Dict[str, str]:
        """为三个算法准备数据"""
        results = {}
        
        try:
            self.logger.info(f"开始为算法准备数据: {csv_file}")
            
            # 1. 为异常检测准备NPY数据
            npy_file = self.prepare_for_anomaly_detection(csv_file)
            results['anomaly'] = npy_file
            
            # 2. 为前驱信号检测准备文件夹结构
            folder_structure = self.prepare_for_precursor_detection(csv_file)
            results['precursor'] = folder_structure['normal_path']
            
            # 3. 为专家规则准备标准CSV
            processed_csv = self.prepare_for_expert_rules(csv_file)
            results['expert'] = processed_csv
            
            self.logger.info("数据预处理完成")
            return results
            
        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            return {}
    
    def prepare_for_anomaly_detection(self, csv_file: str) -> str:
        """为异常检测准备NPY格式数据"""
        try:
            self.logger.info("准备异常检测数据")
            
            # 读取CSV文件
            df = pd.read_csv(csv_file, encoding='utf-8')
            
            # 对齐到12维特征
            aligned_df = self.feature_aligner.align_to_12d(df)
            
            # 标准化处理（使用训练数据的统计信息）
            normalized_df = self.normalize_for_anomaly_detection(aligned_df)
            
            # 转换为NPY格式
            npy_file = self.csv_to_npy_converter.convert_csv_to_npy(
                self.save_temp_csv(normalized_df, "anomaly_input")
            )
            
            return npy_file
            
        except Exception as e:
            self.logger.error(f"异常检测数据准备失败: {e}")
            raise
    
    def prepare_for_precursor_detection(self, csv_file: str) -> Dict[str, str]:
        """为前驱信号检测准备文件夹结构"""
        try:
            self.logger.info("准备前驱信号检测数据")
            
            # 读取CSV文件
            df = pd.read_csv(csv_file, encoding='utf-8')
            
            # 对齐到10维特征
            aligned_df = self.feature_aligner.align_to_10d(df)
            
            # 保存处理后的CSV
            processed_csv = self.save_temp_csv(aligned_df, "precursor_input")
            
            # 创建前驱信号检测所需的文件夹结构
            folder_structure = self.folder_manager.create_earlysignal_structure([processed_csv])
            
            return folder_structure
            
        except Exception as e:
            self.logger.error(f"前驱信号检测数据准备失败: {e}")
            raise
    
    def prepare_for_expert_rules(self, csv_file: str) -> str:
        """为专家规则准备标准CSV格式"""
        try:
            self.logger.info("准备专家规则数据")
            
            # 读取CSV文件
            df = pd.read_csv(csv_file, encoding='utf-8')
            
            # 对齐到10维特征
            aligned_df = self.feature_aligner.align_to_10d(df)
            
            # 专家规则特殊处理
            processed_df = self.process_for_expert_rules(aligned_df)
            
            # 保存处理后的CSV
            processed_csv = self.save_temp_csv(processed_df, "expert_input")
            
            return processed_csv
            
        except Exception as e:
            self.logger.error(f"专家规则数据准备失败: {e}")
            raise
    
    def normalize_for_anomaly_detection(self, df: pd.DataFrame) -> pd.DataFrame:
        """为异常检测进行标准化"""
        try:
            # 这里应该使用训练数据的统计信息进行标准化
            # 简化实现：使用Z-score标准化
            normalized_df = df.copy()
            
            for column in df.columns:
                if df[column].dtype in ['float64', 'int64']:
                    mean_val = df[column].mean()
                    std_val = df[column].std()
                    if std_val > 0:
                        normalized_df[column] = (df[column] - mean_val) / std_val
                    else:
                        normalized_df[column] = 0.0
            
            return normalized_df
            
        except Exception as e:
            self.logger.error(f"异常检测标准化失败: {e}")
            return df
    
    def process_for_expert_rules(self, df: pd.DataFrame) -> pd.DataFrame:
        """为专家规则进行特殊处理"""
        try:
            processed_df = df.copy()
            
            # 确保流量列有合理的默认值
            if 'flow_in' in processed_df.columns:
                processed_df['flow_in'] = processed_df['flow_in'].fillna(30.0)
            if 'flow_out' in processed_df.columns:
                processed_df['flow_out'] = processed_df['flow_out'].fillna(25.0)
            
            # 其他专家规则特殊处理
            # ...
            
            return processed_df
            
        except Exception as e:
            self.logger.error(f"专家规则处理失败: {e}")
            return df
    
    def save_temp_csv(self, df: pd.DataFrame, prefix: str) -> str:
        """保存临时CSV文件"""
        try:
            temp_file = self.temp_manager.create_temp_file(suffix=".csv", prefix=prefix)
            df.to_csv(temp_file, index=False, encoding='utf-8-sig')
            return temp_file
            
        except Exception as e:
            self.logger.error(f"保存临时CSV失败: {e}")
            raise
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            self.temp_manager.cleanup_all()
            self.logger.info("临时文件清理完成")
        except Exception as e:
            self.logger.error(f"临时文件清理失败: {e}")
    
    def validate_csv_file(self, csv_file: str) -> bool:
        """验证CSV文件格式"""
        try:
            if not os.path.exists(csv_file):
                self.logger.error(f"文件不存在: {csv_file}")
                return False
            
            # 尝试读取文件
            df = pd.read_csv(csv_file, encoding='utf-8')
            
            if df.empty:
                self.logger.error(f"文件为空: {csv_file}")
                return False
            
            self.logger.info(f"文件验证通过: {csv_file}, 行数: {len(df)}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件验证失败 {csv_file}: {e}")
            return False
