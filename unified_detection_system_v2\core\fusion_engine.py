"""
统一钻井检测系统V2.0 - 融合引擎
"""

import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from config.config import config_manager
from utils.logger import get_logger
from core.data_processor import DataProcessorV2
from core.algorithm_adapters import (
    PrecursorDetectionAdapter,
    SimplifiedAnomalyDetectionAdapter,
    PureRulesExpertAdapter
)


class UnifiedFusionEngine:
    """统一融合引擎V2.0"""

    def __init__(self, weights: Optional[Dict[str, float]] = None):
        self.config = config_manager.config
        self.logger = get_logger("FusionEngine")
        self.weights = weights or self.config['weights']

        # 初始化组件
        self.data_processor = DataProcessorV2()
        self.adapters = {
            'precursor': PrecursorDetectionAdapter(),
            'anomaly': SimplifiedAnomalyDetectionAdapter(),
            'expert': PureRulesExpertAdapter()
        }

        # 验证权重
        self.validate_weights()

    def validate_weights(self):
        """验证权重配置"""
        try:
            total_weight = sum(self.weights.values())
            if abs(total_weight - 1.0) > 0.01:
                self.logger.warning(f"权重总和不为1.0: {total_weight}, 将进行标准化")
                # 标准化权重
                for key in self.weights:
                    self.weights[key] = self.weights[key] / total_weight
            
            self.logger.info(f"融合权重: {self.weights}")
            
        except Exception as e:
            self.logger.error(f"权重验证失败: {e}")
            # 使用默认权重
            self.weights = {'precursor': 0.5, 'anomaly': 0.3, 'expert': 0.2}

    def predict_single_file(self, csv_file: str) -> Dict[str, Any]:
        """单文件预测"""
        try:
            self.logger.info(f"开始单文件预测: {csv_file}")
            start_time = datetime.now()

            # 数据预处理
            prepared_data = self.data_processor.prepare_data_for_algorithms(csv_file)
            if not prepared_data:
                raise ValueError("数据预处理失败")

            # 执行三个算法
            algorithm_results = self.run_algorithms(prepared_data)

            # 融合结果
            fusion_result = self.fuse_results(algorithm_results)

            # 添加元信息
            end_time = datetime.now()
            fusion_result.update({
                'filename': csv_file,
                'processing_time': (end_time - start_time).total_seconds(),
                'timestamp': end_time.isoformat(),
                'algorithm_results': algorithm_results
            })

            # 清理临时文件
            self.data_processor.cleanup_temp_files()

            self.logger.info("单文件预测完成")
            return fusion_result

        except Exception as e:
            self.logger.error(f"单文件预测失败: {e}")
            return self.get_error_result(csv_file, str(e))

    def run_algorithms(self, prepared_data: Dict[str, str]) -> Dict[str, Dict[str, Any]]:
        """运行三个算法"""
        results = {}
        
        try:
            # 前驱信号检测
            if 'precursor' in prepared_data:
                try:
                    results['precursor'] = self.adapters['precursor'].predict(
                        prepared_data['precursor']
                    )
                except Exception as e:
                    self.logger.error(f"前驱信号检测失败: {e}")
                    results['precursor'] = self.get_algorithm_error_result('precursor', str(e))

            # 异常检测
            if 'anomaly' in prepared_data:
                try:
                    results['anomaly'] = self.adapters['anomaly'].predict(
                        prepared_data['anomaly']
                    )
                except Exception as e:
                    self.logger.error(f"异常检测失败: {e}")
                    results['anomaly'] = self.get_algorithm_error_result('anomaly', str(e))

            # 专家规则
            if 'expert' in prepared_data:
                try:
                    results['expert'] = self.adapters['expert'].predict(
                        prepared_data['expert']
                    )
                except Exception as e:
                    self.logger.error(f"专家规则失败: {e}")
                    results['expert'] = self.get_algorithm_error_result('expert', str(e))

            return results

        except Exception as e:
            self.logger.error(f"算法执行失败: {e}")
            return {}

    def fuse_results(self, algorithm_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """融合算法结果"""
        try:
            self.logger.info("开始结果融合")

            # 检查成功的算法
            successful_algorithms = [
                name for name, result in algorithm_results.items()
                if result.get('success', False)
            ]

            if not successful_algorithms:
                raise ValueError("没有算法成功执行")

            # 计算加权风险分数
            weighted_risk_score = self.calculate_weighted_risk_score(
                algorithm_results, successful_algorithms
            )

            # 计算加权预测标签
            weighted_prediction = self.calculate_weighted_prediction(
                algorithm_results, successful_algorithms
            )

            # 计算置信度
            confidence = self.calculate_confidence(
                algorithm_results, successful_algorithms
            )

            # 构建融合结果
            fusion_result = {
                'risk_score': weighted_risk_score,
                'prediction': weighted_prediction,
                'label': '预警' if weighted_prediction == 1 else '正常',
                'confidence': confidence,
                'successful_algorithms': successful_algorithms,
                'failed_algorithms': [
                    name for name in algorithm_results.keys()
                    if name not in successful_algorithms
                ],
                'fusion_weights': self.weights
            }

            self.logger.info("结果融合完成")
            return fusion_result

        except Exception as e:
            self.logger.error(f"结果融合失败: {e}")
            return {
                'risk_score': 0.0,
                'prediction': 0,
                'label': '正常',
                'confidence': 0.0,
                'error': str(e)
            }

    def calculate_weighted_risk_score(self, results: Dict[str, Dict[str, Any]], 
                                    successful_algorithms: List[str]) -> float:
        """计算加权风险分数"""
        try:
            total_weighted_score = 0.0
            total_weight = 0.0

            for algorithm in successful_algorithms:
                if algorithm in results and algorithm in self.weights:
                    risk_score = results[algorithm].get('risk_score', 0.0)
                    weight = self.weights[algorithm]
                    
                    total_weighted_score += risk_score * weight
                    total_weight += weight

            if total_weight > 0:
                return total_weighted_score / total_weight
            else:
                return 0.0

        except Exception as e:
            self.logger.error(f"计算加权风险分数失败: {e}")
            return 0.0

    def calculate_weighted_prediction(self, results: Dict[str, Dict[str, Any]], 
                                    successful_algorithms: List[str]) -> int:
        """计算加权预测标签"""
        try:
            total_weighted_prediction = 0.0
            total_weight = 0.0

            for algorithm in successful_algorithms:
                if algorithm in results and algorithm in self.weights:
                    prediction = results[algorithm].get('prediction', 0)
                    weight = self.weights[algorithm]
                    
                    total_weighted_prediction += prediction * weight
                    total_weight += weight

            if total_weight > 0:
                weighted_avg = total_weighted_prediction / total_weight
                return 1 if weighted_avg >= 0.5 else 0
            else:
                return 0

        except Exception as e:
            self.logger.error(f"计算加权预测标签失败: {e}")
            return 0

    def calculate_confidence(self, results: Dict[str, Dict[str, Any]], 
                           successful_algorithms: List[str]) -> float:
        """计算置信度"""
        try:
            confidences = []
            
            for algorithm in successful_algorithms:
                if algorithm in results:
                    confidence = results[algorithm].get('confidence', 0.0)
                    confidences.append(confidence)

            if confidences:
                return float(np.mean(confidences))
            else:
                return 0.0

        except Exception as e:
            self.logger.error(f"计算置信度失败: {e}")
            return 0.0

    def get_algorithm_error_result(self, algorithm_name: str, error_msg: str) -> Dict[str, Any]:
        """获取算法错误结果"""
        return {
            'algorithm': algorithm_name,
            'success': False,
            'risk_score': 0.0,
            'prediction': 0,
            'confidence': 0.0,
            'error': error_msg
        }

    def get_error_result(self, filename: str, error_msg: str) -> Dict[str, Any]:
        """获取错误结果"""
        return {
            'filename': filename,
            'risk_score': 0.0,
            'prediction': 0,
            'label': '错误',
            'confidence': 0.0,
            'error': error_msg,
            'timestamp': datetime.now().isoformat()
        }
