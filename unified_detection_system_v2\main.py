"""
统一钻井检测系统V2.0 - 主入口程序
"""

import os
import sys
import argparse
import json
from datetime import datetime
from typing import Dict, Any, Optional

# 处理模块导入（支持直接执行和模块执行两种方式）
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    # 使用绝对导入（适用于直接执行和模块执行）
    from config.config import config_manager
    from core.fusion_engine import UnifiedFusionEngine
    from core.batch_processor import BatchProcessingManager
    from utils.logger import get_logger

except ImportError as e:
    print(f"模块导入失败: {e}")
    print("请确保：")
    print("1. 已安装所有依赖包：pip install -r requirements.txt")
    print("2. 使用正确的执行方式：")
    print("   - 模块执行：cd mix_algo && python -m unified_detection_system_v2.main")
    print("   - 直接执行：cd unified_detection_system_v2 && python main.py")
    print("3. Python路径配置正确")
    sys.exit(1)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='统一钻井检测系统V2.0',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  单文件处理:
    python main.py --input data/test.csv --output results/
    
  批量处理:
    python main.py --input data_folder/ --output results/ --mode batch
    
  自定义权重:
    python main.py --input data/test.csv --weights "precursor:0.6,anomaly:0.2,expert:0.2"
        """
    )
    
    parser.add_argument(
        '--input', '-i',
        required=True,
        help='输入文件路径（单文件模式）或文件夹路径（批量模式）'
    )
    
    parser.add_argument(
        '--output', '-o',
        help='输出文件夹路径（可选，默认为控制台输出）'
    )
    
    parser.add_argument(
        '--mode', '-m',
        choices=['single', 'batch'],
        default='auto',
        help='处理模式：single（单文件）、batch（批量）、auto（自动检测）'
    )
    
    parser.add_argument(
        '--weights', '-w',
        help='算法权重配置，格式：precursor:0.5,anomaly:0.3,expert:0.2'
    )
    
    parser.add_argument(
        '--config', '-c',
        help='配置文件路径（可选）'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出模式'
    )
    
    return parser.parse_args()


def parse_weights(weights_str: str) -> Dict[str, float]:
    """解析权重字符串"""
    try:
        weights = {}
        pairs = weights_str.split(',')
        
        for pair in pairs:
            key, value = pair.split(':')
            weights[key.strip()] = float(value.strip())
        
        # 验证权重总和
        total = sum(weights.values())
        if abs(total - 1.0) > 0.01:
            print(f"警告: 权重总和为 {total}, 将进行标准化")
            for key in weights:
                weights[key] = weights[key] / total
        
        return weights
        
    except Exception as e:
        print(f"权重解析失败: {e}")
        print("使用默认权重")
        return {'precursor': 0.5, 'anomaly': 0.3, 'expert': 0.2}


def determine_mode(input_path: str, specified_mode: str) -> str:
    """确定处理模式"""
    if specified_mode != 'auto':
        return specified_mode
    
    if os.path.isfile(input_path):
        return 'single'
    elif os.path.isdir(input_path):
        return 'batch'
    else:
        raise ValueError(f"输入路径无效: {input_path}")


def print_single_result(filename: str, result: Dict[str, Any]):
    """打印单文件结果"""
    print("\n" + "="*60)
    print("📊 检测结果")
    print("="*60)
    print(f"文件名: {os.path.basename(filename)}")
    print(f"风险分数: {result.get('risk_score', 0.0):.3f}")
    print(f"预测结果: {result.get('label', '未知')}")
    print(f"置信度: {result.get('confidence', 0.0):.3f}")
    print(f"处理时间: {result.get('processing_time', 0.0):.2f}秒")
    
    # 显示算法详情
    if 'successful_algorithms' in result:
        print(f"成功算法: {', '.join(result['successful_algorithms'])}")
    
    if 'failed_algorithms' in result and result['failed_algorithms']:
        print(f"失败算法: {', '.join(result['failed_algorithms'])}")
    
    if 'error' in result:
        print(f"❌ 错误: {result['error']}")
    
    print("="*60)


def print_batch_result(result: Dict[str, Any]):
    """打印批量处理结果"""
    if 'error' in result:
        print(f"❌ 批量处理失败: {result['error']}")
        return
    
    summary = result.get('summary', {})
    processing_info = summary.get('processing_info', {})
    detection_stats = summary.get('detection_statistics', {})
    
    print("\n" + "="*60)
    print("📊 批量处理结果")
    print("="*60)
    print(f"总文件数: {processing_info.get('total_files', 0)}")
    print(f"成功处理: {processing_info.get('successful_files', 0)}")
    print(f"处理失败: {processing_info.get('failed_files', 0)}")
    print(f"成功率: {processing_info.get('success_rate', 0.0):.1%}")
    print(f"处理时间: {processing_info.get('processing_time_seconds', 0.0):.2f}秒")
    print()
    print(f"预警文件: {detection_stats.get('warning_files', 0)}")
    print(f"正常文件: {detection_stats.get('normal_files', 0)}")
    print(f"预警率: {detection_stats.get('warning_rate', 0.0):.1%}")
    print(f"平均风险分数: {detection_stats.get('avg_risk_score', 0.0):.3f}")
    print(f"最高风险分数: {detection_stats.get('max_risk_score', 0.0):.3f}")
    
    # 显示输出文件
    output_files = result.get('output_files', {})
    if output_files:
        print("\n📁 输出文件:")
        for file_type, file_path in output_files.items():
            print(f"  {file_type}: {file_path}")
    
    print("="*60)


def save_single_result(result: Dict[str, Any], output_path: str):
    """保存单文件结果"""
    try:
        # 确保输出目录存在
        os.makedirs(output_path, exist_ok=True)
        
        # 生成输出文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        json_file = os.path.join(output_path, f"result_{timestamp}.json")
        
        # 保存JSON结果
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"结果已保存到: {json_file}")
        
    except Exception as e:
        print(f"保存结果失败: {e}")


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 加载配置
        if args.config:
            config_manager.config_file = args.config
            config_manager.config = config_manager.load_config()
        
        # 解析权重
        weights = None
        if args.weights:
            weights = parse_weights(args.weights)
        
        # 确定处理模式
        mode = determine_mode(args.input, args.mode)
        
        # 初始化日志
        logger = get_logger("Main")
        
        # 创建融合引擎
        fusion_engine = UnifiedFusionEngine(weights=weights)
        
        print(f"统一钻井检测系统V2.0")
        print(f"处理模式: {mode}")
        print(f"输入路径: {args.input}")
        print(f"输出路径: {args.output or '控制台输出'}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 50)
        
        try:
            if mode == 'single':
                # 单文件处理
                result = fusion_engine.predict_single_file(args.input)
                print_single_result(args.input, result)
                
                # 保存结果
                if args.output:
                    save_single_result(result, args.output)
            
            elif mode == 'batch':
                # 批量处理
                batch_processor = BatchProcessingManager(fusion_engine)
                result = batch_processor.process_folder(args.input, args.output)
                print_batch_result(result)
            
            print("-" * 50)
            print(f"处理完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            return 0
            
        except Exception as e:
            logger.error(f"处理失败: {e}")
            print(f"❌ 处理失败: {e}")
            return 1
    
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
